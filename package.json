{"name": "esw_api_pg", "version": "0.0.2", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start:dev": "cross-env nest start --watch", "start:dev:debug": "cross-env nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/infra/framework/main", "start:prod:pm2": "cross-env NODE_ENV=production pm2-runtime ecosystem.config.js", "start:prod:pm2:debug": "cross-env DEBUG_MODE=true yarn start:prod:pm2", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky install", "sentry:sourcemaps": "sentry-cli sourcemaps inject ./dist && sentry-cli releases new $SENTRY_RELEASE && sentry-cli releases files $SENTRY_RELEASE upload-sourcemaps ./dist --url-prefix '~/dist' --validate && sentry-cli releases finalize $SENTRY_RELEASE"}, "dependencies": {"@apollo/server": "^4.12.2", "@keyv/redis": "4.2.0", "@nestjs/apollo": "^12.2.2", "@nestjs/cache-manager": "3.0.0", "@nestjs/common": "^10.4.19", "@nestjs/core": "^10.4.19", "@nestjs/graphql": "^12.2.2", "@nestjs/platform-express": "^10.4.19", "@ntegral/nestjs-sentry": "^4.0.1", "@prisma/client": "^5.22.0", "@sentry/cli": "^2.46.0", "@sentry/nestjs": "^8.55.0", "@sentry/profiling-node": "^8.55.0", "cache-manager": "6.4.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "connect-redis": "^8.0.2", "cross-env": "^7.0.3", "dataloader": "^2.2.3", "dotenv": "^16.5.0", "eslint-plugin-import": "^2.31.0", "express-session": "^1.18.1", "graphql": "^16.11.0", "lodash": "^4.17.21", "mime": "^4.0.7", "moment": "^2.30.1", "pg": "^8.16.0", "pm2": "^5.4.3", "prisma": "^5.22.0", "redis": "4.6.12", "redis-lock": "1.0.0", "redis-om": "0.4.7", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.2"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/compression": "^1.8.1", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.13", "@types/express-session": "^1.18.2", "@types/jest": "29.5.0", "@types/lodash": "^4.17.17", "@types/mime": "3.0.4", "@types/node": "18.15.11", "@types/pg": "^8.15.4", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.3", "jest": "29.5.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.5", "ts-loader": "^9.5.2", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}